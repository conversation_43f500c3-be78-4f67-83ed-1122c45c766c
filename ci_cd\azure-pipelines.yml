trigger:
  - main

pr:
  branches:
    include:
      - '*'

variables:
  - name: applicationCode
    value: "DATA-PLATFORM"
  - name: pythonVersion
    value: '3.12'
  - name: packageName
    value: 'gd-obsd-common'

stages:
  - stage: Test
    displayName: 'Test Package'
    variables:
      - group: ${{ variables.applicationCode }}-INFRA-DEV
    pool:
      name: $(AGENTPOOL)
    jobs:
      - job: TestJob
        displayName: 'Test package'
        steps:
        - task: UsePythonVersion@0
          inputs:
            versionSpec: '$(pythonVersion)'
            addToPath: true
          displayName: 'Using Python $(pythonVersion)'

        - script: |
            python -m pip install --upgrade pip
            python -m pip --version
          displayName: 'Update and Verify pip installation'

        - task: PowerShell@2
          inputs:
            targetType: 'inline'
            script: |
              python -m pip install --upgrade pip
              pip install pytest-azurepipelines build coverage lazy_loader
          displayName: 'Install requirements'

        - task: PowerShell@2
          inputs:
            targetType: 'inline'
            script: |
              # Run tests with coverage
              coverage run --source=gd_obsd_common/ --branch -m pytest tests/ --junitxml=build/test.xml -v
              if ($LASTEXITCODE -ne 0) { exit $LASTEXITCODE }
              coverage xml -i -o build/coverage.xml
          displayName: 'Run Python tests'

        - task: PublishTestResults@2
          displayName: "Publish test results into the Azure DevOps UI"
          condition: succeededOrFailed()
          inputs:
            testResultsFiles: '$(System.DefaultWorkingDirectory)/build/test.xml'
            testRunTitle: 'Publish test results for Python $(pythonVersion)'

        - task: PublishCodeCoverageResults@2
          displayName: "Publish code coverage into the Azure DevOps UI"
          condition: succeededOrFailed()
          inputs:
            summaryFileLocation: '$(System.DefaultWorkingDirectory)/build/coverage.xml'

  - ${{ if eq(variables['Build.SourceBranchName'], 'main') }}:
    - stage: Build
      displayName: 'Build'
      dependsOn: Test
      condition: succeeded()
      variables:
        - group: ${{ variables.applicationCode }}-INFRA-DEV
      pool:
        name: $(AGENTPOOL)

      jobs:
        - job: BuildJob
          displayName: 'Build Package'
          steps:
          - checkout: self
            fetchTags: true
            fetchDepth: 0   # Fetch the last 2 commits
            persistCredentials: true
            displayName: 'Fetching tags from remote'

          - task: PowerShell@2
            displayName: "Generate Tag"
            name: SetVersion
            inputs:
              targetType: 'inline'
              script: |

                # Deleting local remote-tracking branches and tags that have been removed from the remote.
                git fetch --prune --prune-tags

                # Get the latest tag (if any)
                $lastTag = git describe --tags --abbrev=0 --always

                # Find changes in gd_obsd_common/ since the last tag
                $changes = git diff --name-only $lastTag HEAD -- gd_obsd_common/
                Write-Host "Changes detected: $changes"

                if ([string]::IsNullOrWhiteSpace($changes)) {
                    Write-Host "No changes detected in gd_obsd_common folder. Skipping version increment."

                    # Use existing tag or default version without increment
                    $existingTag = $lastTag
                    if (-not $existingTag) {
                        $existingTag = "v1.0"
                    }
                    
                    Write-Host "##vso[task.setvariable variable=TAG_VERSION;isOutput=true]$existingTag"
                    Write-Host "##vso[task.setvariable variable=shouldPublish;isOutput=true]false"

                    Write-Host "Existing tag: $existingTag"
                    Write-Host "Should publish: false"
                    exit 0
                }


                Write-Host "##vso[task.setvariable variable=shouldPublish;isOutput=true]true"
                
                
                # Get latest tag matching 'v*' pattern, sorted by version
                $latestTag = git tag --list "v*" --sort=-v:refname | Select-Object -First 1
                Write-Output "Found tag: $($latestTag -or 'NONE')"
  
                Write-Output "Latest tag found: $latestTag"
          
                if (-not ($latestTag -match '^v\d+\.\d+$')) {
                    Write-Host "No valid or Incorrect tag format found. Using v1.0"
                    $version = [pscustomobject]@{ Major = 1; Minor = 0 }
                } else {
                    $latestTag = $latestTag.Trim()
                    $versionParts = $latestTag.TrimStart('v') -split '\.'
                    $major = [int]$versionParts[0]
                    $minor = [int]$versionParts[1]
                    
                    # Increment minor version
                    $minor += 1
                    
                    # If minor reaches 100, increment major and reset minor
                    if ($minor -eq 100) {
                        $major += 1
                        $minor = 0
                    }
                    
                    $version = [pscustomobject]@{
                        Major = $major
                        Minor = $minor
                    }
                }
                
                $newTag = "v$($version.Major).$($version.Minor)"
                Write-Host "##[section]New Tag: $newTag"
                
                # Set the tag as an output variable
                Write-Host "##vso[task.setvariable variable=TAG_VERSION;isOutput=true]$newTag"
                
                # Create and push tag
                git tag -a $newTag -m "Release $newTag"
                git push origin $newTag
            env:
              SYSTEM_ACCESSTOKEN: $(System.AccessToken)

          - task: UsePythonVersion@0
            inputs:
              versionSpec: '$(pythonVersion)'
              addToPath: true
              architecture: 'x64'
            displayName: 'Using Python $(pythonVersion)'

          - script: |
              python -m pip install --upgrade pip
              python -m pip --version
            displayName: 'Update and Verify pip installation'

          - task: PowerShell@2
            inputs:
              targetType: 'inline'
              script: |
                # Update setup.py
                $setupContent = Get-Content setup.py -Raw
                $setupContent -match "version='([^']+)'" | Out-Null
                $packageVersion = $Matches[1]
                Write-Host "Current setup.py version: $packageVersion"
                $updatedSetupContent = $setupContent -replace "version='([^']+)'", "version='$(SetVersion.TAG_VERSION)'"
                Set-Content -Path setup.py -Value $updatedSetupContent
        
                # Set version as pipeline variable
                Write-Host "##vso[task.setvariable variable=packageVersion]$(SetVersion.TAG_VERSION)"
            displayName: 'Update version in package files'
            enabled: true

          - script: |
              python -m pip install --upgrade "setuptools>=70.1" "wheel>=0.42.0,<0.46"
              python -m build --no-isolation --config-setting=builddir=.
            displayName: 'Build Python package with setuptools'
            condition: succeeded()

          - task: CopyFiles@2
            inputs:
              sourceFolder: 'dist'
              contents: '**'
              targetFolder: '$(Build.ArtifactStagingDirectory)'
            displayName: 'Copy build artifacts'
            enabled: true

          - task: PublishBuildArtifacts@1
            inputs:
              pathToPublish: '$(Build.ArtifactStagingDirectory)'
              artifactName: 'dist'
            displayName: 'Publish build artifacts'
            enabled: true

  - ${{ if eq(variables['Build.SourceBranchName'], 'main') }}:
    - stage: PublishPackage
      displayName: 'Publish To Feed'
      dependsOn: Build
      condition: and(succeeded(), eq(dependencies.Build.outputs['BuildJob.SetVersion.shouldPublish'], 'true'))
      variables:
        - group: ${{ variables.applicationCode }}-INFRA-DEV
        - name: TAG_VERSION
          value: $[ stageDependencies.Build.BuildJob.outputs['SetVersion.TAG_VERSION'] ]
        - name: shouldPublish
          value: $[ stageDependencies.Build.BuildJob.outputs['SetVersion.shouldPublish'] ]
      pool:
        name: $(AGENTPOOL)
      jobs:
        - job: PublishPackage
          displayName: 'Publish to Feed'
          steps:
          - checkout: none

          - task: UsePythonVersion@0
            inputs:
              versionSpec: '$(pythonVersion)'
              addToPath: true
            displayName: 'Using Python $(pythonVersion)'

          - script: |
              python -m pip install --upgrade pip
              python -m pip --version
            displayName: 'Update and Verify pip installation'

          - task: PowerShell@2
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "Would deploy version $(TAG_VERSION) here"
            displayName: 'Display Version'

          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: 'current'
              downloadType: 'single'
              artifactName: 'dist'
              downloadPath: '$(System.ArtifactsDirectory)'

          - task: PipAuthenticate@1
            displayName: 'Pip Authenticate'
            inputs:
              artifactFeeds: '$(ARTIFACT_FEED)'

          - script: |
              pip install twine
            displayName: 'Install publishing tools'

          - task: TwineAuthenticate@1
            inputs:
              artifactFeed: 'GovDigital/$(ARTIFACT_FEED)'
            displayName: 'Twine Authenticate'

          - script: |
              python -m twine upload -r "$(ARTIFACT_FEED)" --config-file $(PYPIRC_PATH) $(System.ArtifactsDirectory)/dist/*
            displayName: 'Upload package to feed'
            env:
              SYSTEM_ACCESSTOKEN: $(System.AccessToken)
              ARTIFACT_FEED: $(ARTIFACT_FEED)
