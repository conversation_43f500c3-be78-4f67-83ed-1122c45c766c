# GD OBSD Common Library

A Python package providing common functionality shared across OBSD streams in the GovDigital platform.

## Introduction

This library contains reusable utilities and components to standardize common operations across OBSD services. It currently includes:

- **DateTime Utilities**: Standardized date/time handling and conversions
- **SharePoint Integration**: Simplified access to SharePoint resources via Microsoft Graph API
- **Teams Alerting**: A simple, standardized way to send adaptive cards to a Teams channel
## Installation

### From Azure DevOps Feed

```bash
# Install the package
pip install gd-obsd-common
```

### For Development

```bash
# Clone the repository
git clone https://dev.azure.com/ADGOV/GovDigital/_git/gd-obsd-common
cd gd-obsd-common

# Install in development mode with dev dependencies
pip install -e ".[dev]"
```

## Requirements

- Python 3.11 or higher
- Core dependencies:
  - pytz
  - azure-identity
  - msgraph-sdk
  - requests
  - pandas
  - pyspark (v3.5.0+)

## Usage Examples

### DateTime Utilities

```python
from gd_obsd_common import DateTimeUtils

# Initialize the utility
dt_utils = DateTimeUtils()

# Convert string to datetime
date_str = '2025-05-07 15:12:00'
dt = dt_utils.str_to_datetime(date_str)
print("Datetime object:", dt)

# Format datetime to string
formatted = dt_utils.datetime_to_str(dt)
print("Formatted date:", formatted)

# Add days to a date
future_date = dt_utils.add_days(dt, 7)
print("Date + 7 days:", future_date)
```

### SharePoint Integration

```python
from gd_obsd_common import SharePoint

# Initialize SharePoint client
sp = SharePoint()

# Read file content from SharePoint
file_url = "https://adgov.sharepoint.com/sites/your-site/Shared%20Documents/path/to/file.xlsx"
file_content = sp.read_file_from_url(file_url)

# Get lists from a SharePoint site
site_url = "https://adgov.sharepoint.com/sites/your-site"
lists = sp.get_lists(site_url, output_type="table")

# Get content from a specific list
list_id = "your-list-id"
list_content = sp.get_list_content(site_url, list_id, output_type="table")
```

### Teams Alerting

```python
from gd_obsd_common import TeamsAlert, AlertMessage, AlertType

def example_usage(teams_alert: TeamsAlert):
    # 1. Simple Success Alert
    success_alert = AlertMessage(
        title="Deployment Successful",
        message="Application v2.1.3 has been successfully deployed to production.",
        alert_type=AlertType.SUCCESS
    )
    teams_alert.send_alert(success_alert)
    
    # 2. Warning Alert with Details
    warning_alert = AlertMessage(
        title="High Memory Usage Detected",
        message="Server memory usage is approaching critical levels.",
        alert_type=AlertType.WARNING
    )
    warning_details = {
        "Server": "prod-web-01",
        "Memory Usage": "87%",
        "Threshold": "85%",
        "Time": "2025-05-28 14:30:00"
    }
    teams_alert.send_alert(warning_alert, metrics=warning_details)
    
    # 3. Error Alert with Action Buttons
    error_alert = AlertMessage(
        title="Service Down",
        message="The payment processing service is currently unavailable.",
        alert_type=AlertType.ERROR
    )
    error_actions = [
        {"name": "View Logs", "url": "https://logs.company.com/payment-service"},
        {"name": "Status Page", "url": "https://status.company.com"},
        {"name": "Restart Service", "url": "https://admin.company.com/services/payment/restart"}
    ]
    teams_alert.send_alert(error_alert, actions=error_actions)
    
    # 4. Critical Monitoring Alert
    critical_alert = AlertMessage(
        title="Database Connection Lost",
        message="Primary database connection has been lost. Failover initiated.",
        alert_type=AlertType.CRITICAL
    )
    critical_metrics = {
        "Database": "prod-db-primary",
        "Status": "Unreachable",
        "Failover Status": "Active",
        "Affected Services": "User Authentication, Orders",
        "Estimated Recovery": "15 minutes"
    }
    critical_actions = [
        {"name": "Database Dashboard", "url": "https://db-monitor.company.com"},
        {"name": "Incident Response", "url": "https://incident.company.com/create"}
    ]
    teams_alert.send_alert(critical_alert, metrics=critical_metrics, actions=critical_actions)
    
    # 5. Info Alert (Simple notification)
    info_alert = AlertMessage(
        title="Scheduled Maintenance",
        message="Routine database maintenance will begin at 2:00 AM UTC.",
        alert_type=AlertType.INFO
    )
    teams_alert.send_alert(info_alert)
```
## Contributing

### Adding New Modules

When developing new common functionality:

1. Create a new folder for your module under `gd_obsd_common/`
2. Create proper `__init__.py` files to ensure correct imports
3. Add any new dependencies to `requirements.txt` and `pyproject.toml`
4. Update the main `gd_obsd_common/__init__.py` to expose your module
5. Add appropriate tests in the `tests/` directory

### Module Structure

```
gd_obsd_common/
├── new_module/
│   ├── __init__.py         # Import and expose your module's classes
│   └── NewModule.py        # Implementation
├── __init__.py             # Update to include: from .new_module import NewModule
```

### Important Notes

- Ensure your module name is correctly referenced in all files (singular `gd_obsd_common`, not plural)
- Follow the existing code style and patterns
- Add comprehensive docstrings and comments
- Write unit tests for all new functionality
- Follow the existing naming conventions outlined in [Naming Conventions](naming_conventions.md)

### Development Workflow

The package supports various extras for different use cases. When developing, you can install specific extras based on your needs:

- Full installation with all dependencies: `pip install -e ".[full]"`
- SharePoint features only: `pip install -e ".[sharepoint]"`
- Development tools and testing: `pip install -e ".[dev]"`
- Multiple extras can be combined: `pip install -e ".[dev,sharepoint]"`

1. Set up your development environment: `pip install -e ".[dev]"`
2. Install pre-commit hooks: `pre-commit install`
3. Make your changes
4. Run tests: `pytest`
5. Check coverage: `coverage run --source=gd_obsd_common/ --branch -m pytest tests/ && coverage report`
6. Submit your changes for review

#### Installation Options

Choose the appropriate installation command based on your requirements:

Core Installation (no extra features):

```bash
pip install gd-obsd-common
```

Full installation (all features and dependencies):   

```bash
pip install gd-obsd-common[full]
```

SharePoint integration only:

```bash
pip install -e ".[sharepoint]"
```

## Quality Assurance

This project uses several tools to maintain code quality:

- **pre-commit**: Runs checks before each commit
- **flake8**: Enforces PEP 8 style guide (max line length: 120)
- **isort**: Sorts imports alphabetically
- **bandit**: Finds common security issues
- **pytest**: Runs unit tests
- **coverage**: Measures test coverage

## CI/CD Pipeline

The project uses Azure DevOps pipelines for continuous integration and deployment with intelligent version management:

### Automated Testing and Quality Checks
- **Every Pull Request**: Runs comprehensive tests and code quality checks
- **Main Branch**: Performs the same tests plus additional build and deployment steps

### Smart Version Management
Our pipeline includes intelligent tagging that only creates new versions when there are actual changes to the library code:

- **Change Detection**: The pipeline automatically checks if any files in the `gd_obsd_common/` folder have been modified since the last release
- **Smart Tagging**:
  - ✅ **New tag created** when changes are detected in the library code
  - ⏭️ **No new tag** when only documentation, tests, or configuration files are updated
- **Efficient Publishing**: The package is only republished when there's a new version, avoiding unnecessary releases

### Pipeline Stages
1. **Test Stage**: Runs unit tests and code coverage analysis
2. **Build Stage** (main branch only):
   - Checks for changes in the library code
   - Creates a new version tag only if changes are found
   - Builds the Python package
3. **Publish Stage** (conditional):
   - Only runs when a new version was created
   - Publishes the package to the organization's private feed

This approach ensures that:
- Version numbers are meaningful and reflect actual code changes
- The package feed isn't cluttered with identical versions
- Developers can confidently update documentation without triggering unnecessary releases
