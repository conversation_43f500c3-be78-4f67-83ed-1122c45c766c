#!/usr/bin/env python

import os
from setuptools import find_packages, setup


def read_file(filename):
    """Read a file and return its contents, handling file not found gracefully."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return ""


def get_requirements():
    """Get requirements from requirements.txt file."""
    requirements_path = "requirements.txt"
    if os.path.exists(requirements_path):
        return read_file(requirements_path).splitlines()
    else:
        # Fallback requirements if file is not found during build
        return [
            "pytz",
            "virtualenv",
            "azure-identity",
            "msgraph-sdk",
            "pandas",
            "pyspark==3.5.0",
            "requests"
        ]


readme = read_file("README.md")
requirements = get_requirements()


setup(
    name='gd-obsd-common',
    version='1.0', # Update by CICD
    description='Common functionality shared across OBSD streams',
    long_description=readme,
    long_description_content_type="text/markdown",
    author='Saqib <PERSON>',
    author_email='<EMAIL>',
    url="https://dev.azure.com/ADGOV/GovDigital/_git/gd-obsd-common",
    packages=find_packages(include=["gd_obsd_common", "gd_obsd_common.*"]),
    include_package_data=True,
    install_requires=["lazy_loader"],
    extras_require={
        "sharepoint": [
            "azure-identity",
            "msgraph-sdk"
        ],
        "sql_framework": [
            "pyspark==3.5.0",
            "pandas"
        ],
        "full": requirements
    },
    zip_safe=False,
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires='>=3.11',
)
